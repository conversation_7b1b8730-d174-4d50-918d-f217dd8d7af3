# TRIADA Elite CTF Team Website

A clean, modern website for the TRIADA Elite Cybersecurity CTF Team.

## What Was Fixed

### Issues Resolved:
1. **Removed development artifacts**: Eliminated all Vite development server files and React HMR code
2. **Cleaned up file structure**: Removed duplicate HTML files and associated `*_files` folders
3. **Separated concerns**: Extracted CSS into a separate `styles.css` file
4. **Fixed broken references**: Removed localhost development server references
5. **Simplified structure**: Created a single-page application with smooth navigation

### Files Removed:
- `TRIADA __ Elite CTF Team_files/` (development artifacts)
- `TRIADA __ Elite CTF Team_achievements_files/`
- `TRIADA __ Elite CTF about_files/`
- `TRIADA __ Elite CTF blog_files/`
- `TRIADA __ Elite CTF contact_files/`
- `TRIADA __ Elite CTF Team_achievements.html`
- `TRIADA __ Elite CTF about.html`
- `TRIADA __ Elite CTF blog.html`
- `TRIADA __ Elite CTF contact.html`

### Current Structure:
```
hostinger_triada/
├── index.html          # Main website file
├── styles.css          # All CSS styles
└── README.md          # This documentation
```

## Features

- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Modern UI**: Clean, professional design with dark theme
- **Smooth Navigation**: Single-page application with smooth scrolling
- **Sections**:
  - Hero section with team branding
  - About section with team information
  - Achievements section showcasing CTF competition results
  - Contact section with email and Discord links
  - Footer with social media links

## Technologies Used

- HTML5
- CSS3 (with Tailwind-like utility classes)
- Vanilla JavaScript for navigation
- Google Fonts (Inter font family)

## How to Use

1. Open `index.html` in any modern web browser
2. The website is fully self-contained and doesn't require a server
3. All navigation is handled via anchor links and smooth scrolling

## Customization

To customize the website:

1. **Content**: Edit the text content in `index.html`
2. **Styling**: Modify the CSS variables and classes in `styles.css`
3. **Colors**: Update the CSS custom properties for theme colors
4. **Sections**: Add or remove sections by editing the HTML structure

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge
- Any modern browser with CSS Grid and Flexbox support

## Contact Information

- Email: <EMAIL>
- Discord: [Add Discord invite link]
- Social Media: [Update social media links in footer]
