<!DOCTYPE html>
<html lang="en" class="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description"
        content="TRIADA - Elite Cybersecurity CTF Team. Explore our achievements, team, events, and insights.">
    <meta name="keywords" content="TRIADA, CTF, Cybersecurity, Hacking, InfoSec, Capture The Flag, Ethical Hacking">
    <title>TRIADA // Elite CTF Team</title>
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <div id="root">
        <div class="min-h-screen flex flex-col bg-background text-foreground font-sans">
            <header
                class="fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-elegant bg-background/90 backdrop-blur-lg shadow-soft border-b border-border">
                <div class="container mx-auto px-4">
                    <div class="flex items-center justify-between h-16">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                                    <span class="text-primary-foreground font-bold text-sm">T</span>
                                </div>
                                <span class="font-bold text-xl tracking-tight">TRIADA</span>
                            </div>
                        </div>
                        <nav class="hidden md:flex items-center space-x-8">
                            <a href="#home"
                                class="text-muted-foreground hover:text-foreground transition-colors">Home</a>
                            <a href="#about"
                                class="text-muted-foreground hover:text-foreground transition-colors">About</a>
                            <a href="#achievements"
                                class="text-muted-foreground hover:text-foreground transition-colors">Achievements</a>
                            <a href="#blog"
                                class="text-muted-foreground hover:text-foreground transition-colors">Blog</a>
                            <a href="#contact"
                                class="text-muted-foreground hover:text-foreground transition-colors">Contact</a>
                        </nav>
                        <button class="md:hidden p-2 rounded-lg hover:bg-secondary transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </header>

            <main class="flex-grow pt-16">
                <!-- Hero Section -->
                <section id="home" class="py-20 bg-gradient-to-br from-background to-secondary/20">
                    <div class="container mx-auto px-4 text-center">
                        <h1 class="text-5xl md:text-7xl font-black mb-6 tracking-tight">
                            <span class="text-primary">TRIADA</span>
                        </h1>
                        <p class="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
                            Elite Cybersecurity CTF Team pushing the boundaries of digital security and ethical hacking
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <button
                                class="btn-clean-primary px-8 py-3 rounded-lg font-medium transition-all hover:scale-105">
                                View Achievements
                            </button>
                            <button
                                class="btn-clean-outline px-8 py-3 rounded-lg font-medium transition-all hover:scale-105">
                                Join Our Team
                            </button>
                        </div>
                    </div>
                </section>

                <!-- About Section -->
                <section id="about" class="py-20">
                    <div class="container mx-auto px-4">
                        <div class="max-w-4xl mx-auto text-center">
                            <h2 class="text-4xl font-bold mb-8">About TRIADA</h2>
                            <p class="text-lg text-muted-foreground mb-12 leading-relaxed">
                                TRIADA is an elite cybersecurity team specializing in Capture The Flag (CTF)
                                competitions.
                                We combine cutting-edge technical skills with innovative problem-solving approaches to
                                tackle
                                the most challenging cybersecurity puzzles.
                            </p>
                            <div class="grid md:grid-cols-3 gap-8">
                                <div class="professional-card p-6">
                                    <div
                                        class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
                                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <h3 class="text-xl font-semibold mb-3">Expert Skills</h3>
                                    <p class="text-muted-foreground">
                                        Our team members are experts in reverse engineering, cryptography, web security,
                                        and forensics.
                                    </p>
                                </div>
                                <div class="professional-card p-6">
                                    <div
                                        class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
                                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                                            </path>
                                        </svg>
                                    </div>
                                    <h3 class="text-xl font-semibold mb-3">Team Collaboration</h3>
                                    <p class="text-muted-foreground">
                                        We work together seamlessly, combining individual strengths to solve complex
                                        challenges.
                                    </p>
                                </div>
                                <div class="professional-card p-6">
                                    <div
                                        class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
                                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    </div>
                                    <h3 class="text-xl font-semibold mb-3">Innovation</h3>
                                    <p class="text-muted-foreground">
                                        We constantly push boundaries and develop new techniques to stay ahead in
                                        cybersecurity.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Achievements Section -->
                <section id="achievements" class="py-20 bg-secondary/10">
                    <div class="container mx-auto px-4">
                        <div class="text-center mb-16">
                            <h2 class="text-4xl font-bold mb-4">Our Achievements</h2>
                            <p class="text-lg text-muted-foreground">
                                Showcasing our success in major CTF competitions worldwide
                            </p>
                        </div>
                        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <div class="professional-card p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <span class="text-2xl font-bold text-primary">#1</span>
                                    <span class="text-sm text-muted-foreground">2024</span>
                                </div>
                                <h3 class="text-xl font-semibold mb-2">Global CTF Championship</h3>
                                <p class="text-muted-foreground">
                                    First place in the international cybersecurity competition with over 500 teams
                                    participating.
                                </p>
                            </div>
                            <div class="professional-card p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <span class="text-2xl font-bold text-primary">#2</span>
                                    <span class="text-sm text-muted-foreground">2024</span>
                                </div>
                                <h3 class="text-xl font-semibold mb-2">DefCon CTF Finals</h3>
                                <p class="text-muted-foreground">
                                    Second place finish at the world's most prestigious hacking competition.
                                </p>
                            </div>
                            <div class="professional-card p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <span class="text-2xl font-bold text-primary">#1</span>
                                    <span class="text-sm text-muted-foreground">2023</span>
                                </div>
                                <h3 class="text-xl font-semibold mb-2">European Cyber Challenge</h3>
                                <p class="text-muted-foreground">
                                    Champions of the European regional competition, demonstrating excellence across all
                                    categories.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Contact Section -->
                <section id="contact" class="py-20">
                    <div class="container mx-auto px-4">
                        <div class="max-w-2xl mx-auto text-center">
                            <h2 class="text-4xl font-bold mb-8">Get In Touch</h2>
                            <p class="text-lg text-muted-foreground mb-12">
                                Interested in joining our team or collaborating with us? We'd love to hear from you.
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <a href="mailto:<EMAIL>"
                                    class="btn-clean-primary px-8 py-3 rounded-lg font-medium transition-all hover:scale-105 inline-block">
                                    Contact Us
                                </a>
                                <a href="#"
                                    class="btn-clean-outline px-8 py-3 rounded-lg font-medium transition-all hover:scale-105 inline-block">
                                    Join Discord
                                </a>
                            </div>
                        </div>
                    </div>
                </section>
            </main>

            <footer class="bg-secondary/20 py-12">
                <div class="container mx-auto px-4">
                    <div class="text-center">
                        <div class="flex items-center justify-center space-x-2 mb-4">
                            <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                                <span class="text-primary-foreground font-bold text-sm">T</span>
                            </div>
                            <span class="font-bold text-xl tracking-tight">TRIADA</span>
                        </div>
                        <p class="text-muted-foreground mb-4">
                            Elite Cybersecurity CTF Team
                        </p>
                        <div class="flex justify-center space-x-6">
                            <a href="#" class="text-muted-foreground hover:text-foreground transition-colors">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                                </svg>
                            </a>
                            <a href="#" class="text-muted-foreground hover:text-foreground transition-colors">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z" />
                                </svg>
                            </a>
                            <a href="#" class="text-muted-foreground hover:text-foreground transition-colors">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z" />
                                </svg>
                            </a>
                        </div>
                        <div class="mt-8 pt-8 border-t border-border">
                            <p class="text-sm text-muted-foreground">
                                © 2024 TRIADA CTF Team. All rights reserved.
                            </p>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <script>
        // Simple navigation functionality
        document.addEventListener('DOMContentLoaded', function () {
            // Smooth scrolling for navigation links
            const navLinks = document.querySelectorAll('a[href^="#"]');
            navLinks.forEach(link => {
                link.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const targetSection = document.querySelector(targetId);
                    if (targetSection) {
                        targetSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Header scroll effect
            const header = document.querySelector('header');
            window.addEventListener('scroll', function () {
                if (window.scrollY > 100) {
                    header.style.transform = 'translateY(0) translateZ(0px)';
                } else {
                    header.style.transform = 'translateY(-100px) translateZ(0px)';
                }
            });
        });
    </script>
</body>

</html>