<!DOCTYPE html>
<!-- saved from url=(0071)https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/blog -->
<html lang="en" class="dark"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <script type="module">import { injectIntoGlobalHook } from "/@react-refresh"
injectIntoGlobalHook(window);
window.$RefreshReg$ = () => {};
window.$RefreshSig$ = () => (type) => type;</script>

        <script type="module" src="./TRIADA __ Elite CTF blog_files/client"></script>

        
        <link rel="icon" type="image/svg+xml" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/vite.svg">
        <meta name="generator" content="Hostinger Horizons">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="TRIADA - Elite Cybersecurity CTF Team. Explore our achievements, team, events, and insights.">
        <meta name="keywords" content="TRIADA, CTF, Cybersecurity, Hacking, InfoSec, Capture The Flag, Ethical Hacking">
        <title>TRIADA // Elite CTF Team</title>
        <script type="module">
window.onerror = (message, source, lineno, colno, errorObj) => {
	const errorDetails = errorObj ? JSON.stringify({
		name: errorObj.name,
		message: errorObj.message,
		stack: errorObj.stack,
		source,
		lineno,
		colno,
	}) : null;

	window.parent.postMessage({
		type: 'horizons-runtime-error',
		message,
		error: errorDetails
	}, '*');
};
</script>
        <script type="module">
const observer = new MutationObserver((mutations) => {
	for (const mutation of mutations) {
		for (const addedNode of mutation.addedNodes) {
			if (
				addedNode.nodeType === Node.ELEMENT_NODE &&
				(
					addedNode.tagName?.toLowerCase() === 'vite-error-overlay' ||
					addedNode.classList?.contains('backdrop')
				)
			) {
				handleViteOverlay(addedNode);
			}
		}
	}
});

observer.observe(document.documentElement, {
	childList: true,
	subtree: true
});

function handleViteOverlay(node) {
	if (!node.shadowRoot) {
		return;
	}

	const backdrop = node.shadowRoot.querySelector('.backdrop');

	if (backdrop) {
		const overlayHtml = backdrop.outerHTML;
		const parser = new DOMParser();
		const doc = parser.parseFromString(overlayHtml, 'text/html');
		const messageBodyElement = doc.querySelector('.message-body');
		const fileElement = doc.querySelector('.file');
		const messageText = messageBodyElement ? messageBodyElement.textContent.trim() : '';
		const fileText = fileElement ? fileElement.textContent.trim() : '';
		const error = messageText + (fileText ? ' File:' + fileText : '');

		window.parent.postMessage({
			type: 'horizons-vite-error',
			error,
		}, '*');
	}
}
</script>
        <script type="module">
const originalConsoleError = console.error;
console.error = function(...args) {
	originalConsoleError.apply(console, args);

	let errorString = '';

	for (let i = 0; i < args.length; i++) {
		const arg = args[i];
		if (arg instanceof Error) {
			errorString = arg.stack || `${arg.name}: ${arg.message}`;
			break;
		}
	}

	if (!errorString) {
		errorString = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
	}

	window.parent.postMessage({
		type: 'horizons-console-error',
		error: errorString
	}, '*');
};
</script>
        <script type="module">
const originalFetch = window.fetch;

window.fetch = function(...args) {
	const url = args[0] instanceof Request ? args[0].url : args[0];

	// Skip WebSocket URLs
	if (url.startsWith('ws:') || url.startsWith('wss:')) {
		return originalFetch.apply(this, args);
	}

	return originalFetch.apply(this, args)
		.then(async response => {
			const contentType = response.headers.get('Content-Type') || '';

			// Exclude HTML document responses
			const isDocumentResponse =
				contentType.includes('text/html') ||
				contentType.includes('application/xhtml+xml');

			if (!response.ok && !isDocumentResponse) {
					const responseClone = response.clone();
					const errorFromRes = await responseClone.text();
					const requestUrl = response.url;
					console.error(`Fetch error from ${requestUrl}: ${errorFromRes}`);
			}

			return response;
		})
		.catch(error => {
			if (!url.match(/.html?$/i)) {
				console.error(error);
			}

			throw error;
		});
};
</script>
      <style type="text/css" data-vite-dev-id="/home/<USER>/websites/GaeHZAXA8/public_html/src/index.css">@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap');

    *, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

    ::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

    /*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

    /*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

    *,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

    ::before,
::after {
  --tw-content: '';
}

    /*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

    html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: "Inter", sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

    /*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

    body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

    /*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

    hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

    /*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

    abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

    /*
Remove the default font size and weight for headings.
*/

    h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

    /*
Reset links to optimize for opt-in styling instead of opt-out.
*/

    a {
  color: inherit;
  text-decoration: inherit;
}

    /*
Add the correct font weight in Edge and Safari.
*/

    b,
strong {
  font-weight: bolder;
}

    /*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

    code,
kbd,
samp,
pre {
  font-family: "Roboto Mono", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

    /*
Add the correct font size in all browsers.
*/

    small {
  font-size: 80%;
}

    /*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

    sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

    sub {
  bottom: -0.25em;
}

    sup {
  top: -0.5em;
}

    /*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

    table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

    /*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

    button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

    /*
Remove the inheritance of text transform in Edge and Firefox.
*/

    button,
select {
  text-transform: none;
}

    /*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

    button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

    /*
Use the modern Firefox focus style for all focusable elements.
*/

    :-moz-focusring {
  outline: auto;
}

    /*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

    :-moz-ui-invalid {
  box-shadow: none;
}

    /*
Add the correct vertical alignment in Chrome and Firefox.
*/

    progress {
  vertical-align: baseline;
}

    /*
Correct the cursor style of increment and decrement buttons in Safari.
*/

    ::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

    /*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

    [type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

    /*
Remove the inner padding in Chrome and Safari on macOS.
*/

    ::-webkit-search-decoration {
  -webkit-appearance: none;
}

    /*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

    ::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

    /*
Add the correct display in Chrome and Safari.
*/

    summary {
  display: list-item;
}

    /*
Removes the default spacing and border for appropriate elements.
*/

    blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

    fieldset {
  margin: 0;
  padding: 0;
}

    legend {
  padding: 0;
}

    ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

    /*
Reset default styling for dialogs.
*/

    dialog {
  padding: 0;
}

    /*
Prevent resizing textareas horizontally by default.
*/

    textarea {
  resize: vertical;
}

    /*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

    input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

    input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

    /*
Set the default cursor for buttons.
*/

    button,
[role="button"] {
  cursor: pointer;
}

    /*
Make sure disabled buttons don't get the pointer cursor.
*/

    :disabled {
  cursor: default;
}

    /*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

    img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

    /*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

    img,
video {
  max-width: 100%;
  height: auto;
}

    /* Make elements with the HTML hidden attribute stay hidden by default */

    [hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

    :root {
        --background: 240 10% 96%; /* Light Mode: Very Light Grey/Off-White */
        --foreground: 240 10% 4%; /* Light Mode: Deep, almost black blue */

        --card: 240 10% 100%; /* Light Mode: White */
        --card-foreground: 240 10% 7%;

        --popover: 240 10% 100%;
        --popover-foreground: 240 10% 7%;

        --primary: 240 10% 10%; /* Light Mode: Dark Blue/Black for primary actions */
        --primary-foreground: 240 10% 98%; /* Light Mode: White text on primary */

        --secondary: 240 10% 85%; /* Light Mode: Light Grey for secondary elements */
        --secondary-foreground: 240 10% 15%; 

        --muted: 240 10% 90%;
        --muted-foreground: 240 5% 45%; /* Light Mode: Softer muted text */

        --accent: 240 10% 20%; 
        --accent-foreground: 240 10% 90%;

        --destructive: 0 70% 50%; /* Light Mode: Professional red */
        --destructive-foreground: 0 0% 98%;

        --border: 240 10% 88%; /* Light Mode: Subtle border color */
        --input: 240 10% 92%; 
        --ring: 240 10% 30%; 

        --radius: 0.5rem;
      }

    .dark {
        --background: 240 10% 4%; /* Dark Mode: Deep, almost black blue */
        --foreground: 240 10% 95%; /* Dark Mode: Soft off-white */

        --card: 240 10% 7%; /* Dark Mode: Slightly lighter than background */
        --card-foreground: 240 10% 90%;

        --popover: 240 10% 3%;
        --popover-foreground: 240 10% 90%;

        --primary: 240 10% 90%; /* Dark Mode: Professional White/Light Grey for primary actions */
        --primary-foreground: 240 10% 4%; /* Dark Mode: Dark text on primary buttons */

        --secondary: 240 10% 25%; /* Dark Mode: Muted Blue/Grey for secondary elements */
        --secondary-foreground: 240 10% 85%; 

        --muted: 240 10% 15%;
        --muted-foreground: 240 5% 55%; /* Dark Mode: Softer muted text */

        --accent: 240 10% 80%; 
        --accent-foreground: 240 10% 10%;

        --destructive: 0 60% 50%; /* Dark Mode: A less intense, professional red */
        --destructive-foreground: 0 0% 98%;

        --border: 240 10% 12%; /* Dark Mode: Subtle border color */
        --input: 240 10% 10%; 
        --ring: 240 10% 70%; /* Dark Mode: Softer focus ring */
      }

    *{
  border-color: hsl(var(--border));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  animation-duration: 300ms;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

    body{
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
        font-family: 'Inter', sans-serif;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
}

    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

    ::-webkit-scrollbar-track {
        background: hsl(var(--background));
      }

    ::-webkit-scrollbar-thumb {
        background: hsl(var(--border)); 
        border-radius: var(--radius);
      }

    ::-webkit-scrollbar-thumb:hover {
        background: hsl(var(--secondary));
      }

    .container{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}

    @media (min-width: 1280px){

  .container{
    max-width: 1280px;
  }
}

    @media (min-width: 1440px){

  .container{
    max-width: 1440px;
  }
}

    .sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

    .pointer-events-none{
  pointer-events: none;
}

    .pointer-events-auto{
  pointer-events: auto;
}

    .visible{
  visibility: visible;
}

    .fixed{
  position: fixed;
}

    .absolute{
  position: absolute;
}

    .relative{
  position: relative;
}

    .inset-0{
  inset: 0px;
}

    .left-0{
  left: 0px;
}

    .left-\[50\%\]{
  left: 50%;
}

    .right-0{
  right: 0px;
}

    .right-2{
  right: 0.5rem;
}

    .right-4{
  right: 1rem;
}

    .top-0{
  top: 0px;
}

    .top-2{
  top: 0.5rem;
}

    .top-4{
  top: 1rem;
}

    .top-\[50\%\]{
  top: 50%;
}

    .z-10{
  z-index: 10;
}

    .z-50{
  z-index: 50;
}

    .z-\[100\]{
  z-index: 100;
}

    .mx-auto{
  margin-left: auto;
  margin-right: auto;
}

    .my-12{
  margin-top: 3rem;
  margin-bottom: 3rem;
}

    .mb-1{
  margin-bottom: 0.25rem;
}

    .mb-10{
  margin-bottom: 2.5rem;
}

    .mb-12{
  margin-bottom: 3rem;
}

    .mb-16{
  margin-bottom: 4rem;
}

    .mb-2{
  margin-bottom: 0.5rem;
}

    .mb-3{
  margin-bottom: 0.75rem;
}

    .mb-4{
  margin-bottom: 1rem;
}

    .mb-5{
  margin-bottom: 1.25rem;
}

    .mb-6{
  margin-bottom: 1.5rem;
}

    .mb-8{
  margin-bottom: 2rem;
}

    .ml-2{
  margin-left: 0.5rem;
}

    .ml-2\.5{
  margin-left: 0.625rem;
}

    .mr-1\.5{
  margin-right: 0.375rem;
}

    .mr-2{
  margin-right: 0.5rem;
}

    .mr-2\.5{
  margin-right: 0.625rem;
}

    .mr-3\.5{
  margin-right: 0.875rem;
}

    .mr-\[0\.25em\]{
  margin-right: 0.25em;
}

    .mt-0\.5{
  margin-top: 0.125rem;
}

    .mt-1{
  margin-top: 0.25rem;
}

    .mt-10{
  margin-top: 2.5rem;
}

    .mt-2{
  margin-top: 0.5rem;
}

    .mt-3{
  margin-top: 0.75rem;
}

    .inline-block{
  display: inline-block;
}

    .flex{
  display: flex;
}

    .inline-flex{
  display: inline-flex;
}

    .grid{
  display: grid;
}

    .hidden{
  display: none;
}

    .aspect-square{
  aspect-ratio: 1 / 1;
}

    .h-10{
  height: 2.5rem;
}

    .h-11{
  height: 2.75rem;
}

    .h-12{
  height: 3rem;
}

    .h-14{
  height: 3.5rem;
}

    .h-20{
  height: 5rem;
}

    .h-24{
  height: 6rem;
}

    .h-3\.5{
  height: 0.875rem;
}

    .h-4{
  height: 1rem;
}

    .h-5{
  height: 1.25rem;
}

    .h-6{
  height: 1.5rem;
}

    .h-7{
  height: 1.75rem;
}

    .h-8{
  height: 2rem;
}

    .h-9{
  height: 2.25rem;
}

    .h-auto{
  height: auto;
}

    .h-full{
  height: 100%;
}

    .max-h-screen{
  max-height: 100vh;
}

    .min-h-\[100px\]{
  min-height: 100px;
}

    .min-h-\[70vh\]{
  min-height: 70vh;
}

    .min-h-\[75vh\]{
  min-height: 75vh;
}

    .min-h-screen{
  min-height: 100vh;
}

    .w-10{
  width: 2.5rem;
}

    .w-12{
  width: 3rem;
}

    .w-14{
  width: 3.5rem;
}

    .w-24{
  width: 6rem;
}

    .w-3\.5{
  width: 0.875rem;
}

    .w-4{
  width: 1rem;
}

    .w-5{
  width: 1.25rem;
}

    .w-6{
  width: 1.5rem;
}

    .w-7{
  width: 1.75rem;
}

    .w-8{
  width: 2rem;
}

    .w-9{
  width: 2.25rem;
}

    .w-full{
  width: 100%;
}

    .max-w-2xl{
  max-width: 42rem;
}

    .max-w-3xl{
  max-width: 48rem;
}

    .max-w-lg{
  max-width: 32rem;
}

    .max-w-md{
  max-width: 28rem;
}

    .max-w-xl{
  max-width: 36rem;
}

    .flex-shrink-0{
  flex-shrink: 0;
}

    .shrink-0{
  flex-shrink: 0;
}

    .flex-grow{
  flex-grow: 1;
}

    .translate-x-\[-50\%\]{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

    .translate-y-\[-50\%\]{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

    .transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

    @keyframes pulse{

  50%{
    opacity: .5;
  }
}

    .animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

    .grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

    .grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

    .flex-col{
  flex-direction: column;
}

    .flex-col-reverse{
  flex-direction: column-reverse;
}

    .flex-wrap{
  flex-wrap: wrap;
}

    .items-start{
  align-items: flex-start;
}

    .items-center{
  align-items: center;
}

    .justify-center{
  justify-content: center;
}

    .justify-between{
  justify-content: space-between;
}

    .justify-around{
  justify-content: space-around;
}

    .gap-1{
  gap: 0.25rem;
}

    .gap-10{
  gap: 2.5rem;
}

    .gap-4{
  gap: 1rem;
}

    .gap-6{
  gap: 1.5rem;
}

    .gap-8{
  gap: 2rem;
}

    .gap-x-4{
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

    .gap-x-5{
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
}

    .gap-y-1\.5{
  row-gap: 0.375rem;
}

    .gap-y-2{
  row-gap: 0.5rem;
}

    .space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

    .space-x-2\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.625rem * var(--tw-space-x-reverse));
  margin-left: calc(0.625rem * calc(1 - var(--tw-space-x-reverse)));
}

    .space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

    .space-x-3\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.875rem * var(--tw-space-x-reverse));
  margin-left: calc(0.875rem * calc(1 - var(--tw-space-x-reverse)));
}

    .space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

    .space-x-5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.25rem * var(--tw-space-x-reverse));
  margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
}

    .space-y-1\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}

    .space-y-16 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(4rem * var(--tw-space-y-reverse));
}

    .space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

    .space-y-24 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(6rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(6rem * var(--tw-space-y-reverse));
}

    .space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

    .space-y-5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}

    .space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

    .space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

    .overflow-hidden{
  overflow: hidden;
}

    .whitespace-nowrap{
  white-space: nowrap;
}

    .rounded-full{
  border-radius: 9999px;
}

    .rounded-lg{
  border-radius: var(--radius);
}

    .rounded-md{
  border-radius: calc(var(--radius) - 0.125rem);
}

    .rounded-sm{
  border-radius: calc(var(--radius) - 0.25rem);
}

    .rounded-xl{
  border-radius: calc(var(--radius) + 0.25rem);
}

    .rounded-t-xl{
  border-top-left-radius: calc(var(--radius) + 0.25rem);
  border-top-right-radius: calc(var(--radius) + 0.25rem);
}

    .rounded-tl-xl{
  border-top-left-radius: calc(var(--radius) + 0.25rem);
}

    .rounded-tr-xl{
  border-top-right-radius: calc(var(--radius) + 0.25rem);
}

    .border{
  border-width: 1px;
}

    .border-2{
  border-width: 2px;
}

    .border-b{
  border-bottom-width: 1px;
}

    .border-t{
  border-top-width: 1px;
}

    .border-dashed{
  border-style: dashed;
}

    .border-border{
  border-color: hsl(var(--border));
}

    .border-border\/70{
  border-color: hsl(var(--border) / 0.7);
}

    .border-destructive{
  border-color: hsl(var(--destructive));
}

    .border-input{
  border-color: hsl(var(--input));
}

    .border-primary\/20{
  border-color: hsl(var(--primary) / 0.2);
}

    .border-primary\/50{
  border-color: hsl(var(--primary) / 0.5);
}

    .border-yellow-500\/40{
  border-color: rgb(234 179 8 / 0.4);
}

    .bg-background{
  background-color: hsl(var(--background));
}

    .bg-background\/80{
  background-color: hsl(var(--background) / 0.8);
}

    .bg-background\/90{
  background-color: hsl(var(--background) / 0.9);
}

    .bg-background\/95{
  background-color: hsl(var(--background) / 0.95);
}

    .bg-card{
  background-color: hsl(var(--card));
}

    .bg-card\/90{
  background-color: hsl(var(--card) / 0.9);
}

    .bg-destructive{
  background-color: hsl(var(--destructive));
}

    .bg-green-500\/15{
  background-color: rgb(34 197 94 / 0.15);
}

    .bg-muted{
  background-color: hsl(var(--muted));
}

    .bg-muted\/30{
  background-color: hsl(var(--muted) / 0.3);
}

    .bg-primary{
  background-color: hsl(var(--primary));
}

    .bg-primary\/10{
  background-color: hsl(var(--primary) / 0.1);
}

    .bg-primary\/5{
  background-color: hsl(var(--primary) / 0.05);
}

    .bg-secondary{
  background-color: hsl(var(--secondary));
}

    .bg-transparent{
  background-color: transparent;
}

    .bg-yellow-500\/10{
  background-color: rgb(234 179 8 / 0.1);
}

    .bg-yellow-500\/15{
  background-color: rgb(234 179 8 / 0.15);
}

    .p-0{
  padding: 0px;
}

    .p-1{
  padding: 0.25rem;
}

    .p-3{
  padding: 0.75rem;
}

    .p-4{
  padding: 1rem;
}

    .p-5{
  padding: 1.25rem;
}

    .p-6{
  padding: 1.5rem;
}

    .p-8{
  padding: 2rem;
}

    .px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

    .px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

    .px-3\.5{
  padding-left: 0.875rem;
  padding-right: 0.875rem;
}

    .px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}

    .px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}

    .py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

    .py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

    .py-10{
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

    .py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}

    .py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}

    .py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

    .py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

    .py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}

    .pb-3{
  padding-bottom: 0.75rem;
}

    .pb-4{
  padding-bottom: 1rem;
}

    .pb-8{
  padding-bottom: 2rem;
}

    .pr-8{
  padding-right: 2rem;
}

    .pt-0{
  padding-top: 0px;
}

    .pt-1\.5{
  padding-top: 0.375rem;
}

    .pt-2{
  padding-top: 0.5rem;
}

    .pt-24{
  padding-top: 6rem;
}

    .pt-3{
  padding-top: 0.75rem;
}

    .pt-4{
  padding-top: 1rem;
}

    .pt-8{
  padding-top: 2rem;
}

    .text-center{
  text-align: center;
}

    .font-mono{
  font-family: "Roboto Mono", monospace;
}

    .font-sans{
  font-family: "Inter", sans-serif;
}

    .text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}

    .text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}

    .text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}

    .text-5xl{
  font-size: 3rem;
  line-height: 1;
}

    .text-7xl{
  font-size: 4.5rem;
  line-height: 1;
}

    .text-8xl{
  font-size: 6rem;
  line-height: 1;
}

    .text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}

    .text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}

    .text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}

    .text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}

    .text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}

    .font-black{
  font-weight: 900;
}

    .font-bold{
  font-weight: 700;
}

    .font-medium{
  font-weight: 500;
}

    .font-semibold{
  font-weight: 600;
}

    .uppercase{
  text-transform: uppercase;
}

    .leading-none{
  line-height: 1;
}

    .leading-relaxed{
  line-height: 1.625;
}

    .leading-tight{
  line-height: 1.25;
}

    .tracking-normal{
  letter-spacing: 0em;
}

    .tracking-tight{
  letter-spacing: -0.025em;
}

    .tracking-wide{
  letter-spacing: 0.025em;
}

    .text-card-foreground{
  color: hsl(var(--card-foreground));
}

    .text-destructive{
  color: hsl(var(--destructive));
}

    .text-destructive-foreground{
  color: hsl(var(--destructive-foreground));
}

    .text-foreground{
  color: hsl(var(--foreground));
}

    .text-foreground\/50{
  color: hsl(var(--foreground) / 0.5);
}

    .text-green-300{
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}

    .text-muted-foreground{
  color: hsl(var(--muted-foreground));
}

    .text-muted-foreground\/60{
  color: hsl(var(--muted-foreground) / 0.6);
}

    .text-muted-foreground\/80{
  color: hsl(var(--muted-foreground) / 0.8);
}

    .text-primary{
  color: hsl(var(--primary));
}

    .text-primary-foreground{
  color: hsl(var(--primary-foreground));
}

    .text-primary\/70{
  color: hsl(var(--primary) / 0.7);
}

    .text-primary\/80{
  color: hsl(var(--primary) / 0.8);
}

    .text-primary\/90{
  color: hsl(var(--primary) / 0.9);
}

    .text-secondary{
  color: hsl(var(--secondary));
}

    .text-secondary-foreground{
  color: hsl(var(--secondary-foreground));
}

    .text-yellow-300{
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}

    .text-yellow-300\/90{
  color: rgb(253 224 71 / 0.9);
}

    .text-yellow-500\/60{
  color: rgb(234 179 8 / 0.6);
}

    .underline-offset-4{
  text-underline-offset: 4px;
}

    .opacity-0{
  opacity: 0;
}

    .opacity-70{
  opacity: 0.7;
}

    .opacity-75{
  opacity: 0.75;
}

    .opacity-90{
  opacity: 0.9;
}

    .opacity-\[0\.02\]{
  opacity: 0.02;
}

    .shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

    .shadow-minimal{
  --tw-shadow: 0 1px 3px hsl(var(--border) / 0.5);
  --tw-shadow-colored: 0 1px 3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

    .shadow-soft{
  --tw-shadow: 0 2px 10px hsl(var(--background) / 0.1);
  --tw-shadow-colored: 0 2px 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

    .shadow-soft-lg{
  --tw-shadow: 0 10px 30px hsl(var(--background) / 0.15);
  --tw-shadow-colored: 0 10px 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

    .shadow-soft-md{
  --tw-shadow: 0 5px 20px hsl(var(--background) / 0.12);
  --tw-shadow-colored: 0 5px 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

    .outline{
  outline-style: solid;
}

    .ring-offset-background{
  --tw-ring-offset-color: hsl(var(--background));
}

    .blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

    .filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

    .backdrop-blur-lg{
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

    .backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

    .transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

    .transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

    .transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

    .transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

    .transition-shadow{
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

    .transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

    .duration-200{
  transition-duration: 200ms;
}

    .duration-300{
  transition-duration: 300ms;
}

    .ease-elegant{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

    @keyframes enter{

  from{
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

    @keyframes exit{

  to{
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

    .duration-200{
  animation-duration: 200ms;
}

    .duration-300{
  animation-duration: 300ms;
}

    .ease-elegant{
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

    .professional-card { /* Combined from web3-card for professional look */ border-radius: var(--radius); border-width: 1px; border-color: hsl(var(--border)); background-color: hsl(var(--card)); --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); transition-property: all; transition-duration: 300ms; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); animation-duration: 300ms; animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 15px hsl(var(--background) / 0.15); /* Adjusted shadow opacity */
    }

    .professional-card:hover{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); 
      box-shadow: 0 8px 25px hsl(var(--background) / 0.2); /* Adjusted shadow opacity */
      transform: translateY(-2px);
}

    .btn-clean{
  font-weight: 500;
  letter-spacing: 0em;
}

    .btn-clean-primary{
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  font-weight: 500;
  letter-spacing: 0em;
}

    .btn-clean-primary:hover{
  background-color: hsl(var(--primary) / 0.85);
}

    .btn-clean-primary { /* Adjusted hover opacity */
    }

    .btn-clean-outline{
  border-width: 1px;
  border-color: hsl(var(--primary) / 0.5);
  color: hsl(var(--primary));
  font-weight: 500;
  letter-spacing: 0em;
}

    .btn-clean-outline:hover{
  background-color: hsl(var(--primary) / 0.1);
  color: hsl(var(--primary));
}

    .btn-clean-outline { /* Ensure primary text color */
    }

    h1, h2, h3, h4, h5, h6{
  font-weight: 600;
  letter-spacing: -0.025em;
  color: hsl(var(--foreground));
}

    .font-professional-sans { 
      font-family: 'Inter', sans-serif;
    }

    .font-professional-mono { 
      font-family: 'Roboto Mono', monospace;
    }

    /* Elegant focus state for interactive elements */

    *:focus-visible {
        outline: 2px solid hsl(var(--ring));
        outline-offset: 2px;
        border-radius: var(--radius);
    }

    /* Removing old glitch and terminal caret styles that are not needed */

    .glitch-text, .glitch-text::before, .glitch-text::after, .terminal-caret {
      content: none;
      animation: none;
      display: none;
    }

    .text-shadow-subtle {
      text-shadow: none;
    }

    .file\:border-0::file-selector-button{
  border-width: 0px;
}

    .file\:bg-transparent::file-selector-button{
  background-color: transparent;
}

    .file\:text-sm::file-selector-button{
  font-size: 0.875rem;
  line-height: 1.25rem;
}

    .file\:font-medium::file-selector-button{
  font-weight: 500;
}

    .placeholder\:text-muted-foreground::-moz-placeholder{
  color: hsl(var(--muted-foreground));
}

    .placeholder\:text-muted-foreground::placeholder{
  color: hsl(var(--muted-foreground));
}

    .hover\:scale-\[1\.03\]:hover{
  --tw-scale-x: 1.03;
  --tw-scale-y: 1.03;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

    .hover\:border-primary\/20:hover{
  border-color: hsl(var(--primary) / 0.2);
}

    .hover\:border-primary\/30:hover{
  border-color: hsl(var(--primary) / 0.3);
}

    .hover\:border-primary\/40:hover{
  border-color: hsl(var(--primary) / 0.4);
}

    .hover\:border-secondary\/30:hover{
  border-color: hsl(var(--secondary) / 0.3);
}

    .hover\:bg-accent:hover{
  background-color: hsl(var(--accent));
}

    .hover\:bg-destructive\/85:hover{
  background-color: hsl(var(--destructive) / 0.85);
}

    .hover\:bg-primary\/10:hover{
  background-color: hsl(var(--primary) / 0.1);
}

    .hover\:bg-primary\/85:hover{
  background-color: hsl(var(--primary) / 0.85);
}

    .hover\:bg-secondary:hover{
  background-color: hsl(var(--secondary));
}

    .hover\:bg-secondary\/10:hover{
  background-color: hsl(var(--secondary) / 0.1);
}

    .hover\:bg-secondary\/85:hover{
  background-color: hsl(var(--secondary) / 0.85);
}

    .hover\:text-accent-foreground:hover{
  color: hsl(var(--accent-foreground));
}

    .hover\:text-foreground:hover{
  color: hsl(var(--foreground));
}

    .hover\:text-primary:hover{
  color: hsl(var(--primary));
}

    .hover\:underline:hover{
  text-decoration-line: underline;
}

    .hover\:opacity-100:hover{
  opacity: 1;
}

    .hover\:shadow-soft-lg:hover{
  --tw-shadow: 0 10px 30px hsl(var(--background) / 0.15);
  --tw-shadow-colored: 0 10px 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

    .hover\:shadow-soft-md:hover{
  --tw-shadow: 0 5px 20px hsl(var(--background) / 0.12);
  --tw-shadow-colored: 0 5px 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

    .focus\:border-primary\/70:focus{
  border-color: hsl(var(--primary) / 0.7);
}

    .focus\:opacity-100:focus{
  opacity: 1;
}

    .focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

    .focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

    .focus\:ring-ring:focus{
  --tw-ring-color: hsl(var(--ring));
}

    .focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}

    .focus-visible\:outline-none:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

    .focus-visible\:ring-2:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

    .focus-visible\:ring-ring:focus-visible{
  --tw-ring-color: hsl(var(--ring));
}

    .focus-visible\:ring-offset-2:focus-visible{
  --tw-ring-offset-width: 2px;
}

    .active\:scale-\[0\.98\]:active{
  --tw-scale-x: 0.98;
  --tw-scale-y: 0.98;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

    .disabled\:pointer-events-none:disabled{
  pointer-events: none;
}

    .disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}

    .disabled\:opacity-50:disabled{
  opacity: 0.5;
}

    .disabled\:opacity-60:disabled{
  opacity: 0.6;
}

    .group:hover .group-hover\:scale-105{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

    .group:hover .group-hover\:border-primary{
  border-color: hsl(var(--primary));
}

    .group:hover .group-hover\:bg-primary{
  background-color: hsl(var(--primary));
}

    .group:hover .group-hover\:text-foreground{
  color: hsl(var(--foreground));
}

    .group:hover .group-hover\:text-primary{
  color: hsl(var(--primary));
}

    .group:hover .group-hover\:text-primary-foreground{
  color: hsl(var(--primary-foreground));
}

    .group:hover .group-hover\:text-primary\/80{
  color: hsl(var(--primary) / 0.8);
}

    .group:hover .group-hover\:text-primary\/90{
  color: hsl(var(--primary) / 0.9);
}

    .group:hover .group-hover\:underline{
  text-decoration-line: underline;
}

    .group:hover .group-hover\:opacity-100{
  opacity: 1;
}

    .group.destructive .group-\[\.destructive\]\:border-destructive\/30{
  border-color: hsl(var(--destructive) / 0.3);
}

    .group.destructive .group-\[\.destructive\]\:text-red-300{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

    .group.destructive .group-\[\.destructive\]\:hover\:border-destructive\/30:hover{
  border-color: hsl(var(--destructive) / 0.3);
}

    .group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover{
  background-color: hsl(var(--destructive));
}

    .group.destructive .group-\[\.destructive\]\:hover\:text-destructive-foreground:hover{
  color: hsl(var(--destructive-foreground));
}

    .group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover{
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}

    .group.destructive .group-\[\.destructive\]\:focus\:ring-destructive:focus{
  --tw-ring-color: hsl(var(--destructive));
}

    .group.destructive .group-\[\.destructive\]\:focus\:ring-red-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));
}

    .group.destructive .group-\[\.destructive\]\:focus\:ring-offset-red-600:focus{
  --tw-ring-offset-color: #dc2626;
}

    .peer:disabled ~ .peer-disabled\:cursor-not-allowed{
  cursor: not-allowed;
}

    .peer:disabled ~ .peer-disabled\:opacity-70{
  opacity: 0.7;
}

    .data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"]{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

    .data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"]{
  --tw-translate-x: var(--radix-toast-swipe-end-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

    .data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"]{
  --tw-translate-x: var(--radix-toast-swipe-move-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

    .data-\[state\=active\]\:bg-primary[data-state="active"]{
  background-color: hsl(var(--primary));
}

    .data-\[state\=active\]\:bg-primary\/10[data-state="active"]{
  background-color: hsl(var(--primary) / 0.1);
}

    .data-\[state\=open\]\:bg-accent[data-state="open"]{
  background-color: hsl(var(--accent));
}

    .data-\[state\=active\]\:text-primary[data-state="active"]{
  color: hsl(var(--primary));
}

    .data-\[state\=active\]\:text-primary-foreground[data-state="active"]{
  color: hsl(var(--primary-foreground));
}

    .data-\[state\=open\]\:text-muted-foreground[data-state="open"]{
  color: hsl(var(--muted-foreground));
}

    .data-\[state\=active\]\:shadow-none[data-state="active"]{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

    .data-\[state\=active\]\:shadow-sm[data-state="active"]{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

    .data-\[swipe\=move\]\:transition-none[data-swipe="move"]{
  transition-property: none;
}

    .data-\[state\=open\]\:animate-in[data-state="open"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

    .data-\[state\=closed\]\:animate-out[data-state="closed"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

    .data-\[swipe\=end\]\:animate-out[data-swipe="end"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

    .data-\[state\=closed\]\:fade-out-0[data-state="closed"]{
  --tw-exit-opacity: 0;
}

    .data-\[state\=closed\]\:fade-out-80[data-state="closed"]{
  --tw-exit-opacity: 0.8;
}

    .data-\[state\=open\]\:fade-in-0[data-state="open"]{
  --tw-enter-opacity: 0;
}

    .data-\[state\=closed\]\:zoom-out-95[data-state="closed"]{
  --tw-exit-scale: .95;
}

    .data-\[state\=open\]\:zoom-in-95[data-state="open"]{
  --tw-enter-scale: .95;
}

    .data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"]{
  --tw-exit-translate-x: -50%;
}

    .data-\[state\=closed\]\:slide-out-to-right-full[data-state="closed"]{
  --tw-exit-translate-x: 100%;
}

    .data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"]{
  --tw-exit-translate-y: -48%;
}

    .data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"]{
  --tw-enter-translate-x: -50%;
}

    .data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"]{
  --tw-enter-translate-y: -48%;
}

    .data-\[state\=open\]\:slide-in-from-top-full[data-state="open"]{
  --tw-enter-translate-y: -100%;
}

    @media (min-width: 640px){

  .sm\:bottom-0{
    bottom: 0px;
  }

  .sm\:right-0{
    right: 0px;
  }

  .sm\:top-auto{
    top: auto;
  }

  .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:flex-col{
    flex-direction: column;
  }

  .sm\:justify-end{
    justify-content: flex-end;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-5 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1.25rem * var(--tw-space-x-reverse));
    margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:rounded-lg{
    border-radius: var(--radius);
  }

  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:text-left{
    text-align: left;
  }

  .sm\:text-8xl{
    font-size: 6rem;
    line-height: 1;
  }

  .data-\[state\=open\]\:sm\:slide-in-from-bottom-full[data-state="open"]{
    --tw-enter-translate-y: 100%;
  }
}

    @media (min-width: 768px){

  .md\:col-span-1{
    grid-column: span 1 / span 1;
  }

  .md\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .md\:flex{
    display: flex;
  }

  .md\:hidden{
    display: none;
  }

  .md\:h-8{
    height: 2rem;
  }

  .md\:w-8{
    width: 2rem;
  }

  .md\:max-w-\[420px\]{
    max-width: 420px;
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:items-start{
    align-items: flex-start;
  }

  .md\:justify-end{
    justify-content: flex-end;
  }

  .md\:justify-center{
    justify-content: center;
  }

  .md\:gap-8{
    gap: 2rem;
  }

  .md\:space-y-20 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(5rem * var(--tw-space-y-reverse));
  }

  .md\:space-y-32 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(8rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(8rem * var(--tw-space-y-reverse));
  }

  .md\:py-20{
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .md\:pt-32{
    padding-top: 8rem;
  }

  .md\:text-left{
    text-align: left;
  }

  .md\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-9xl{
    font-size: 8rem;
    line-height: 1;
  }
}

    @media (min-width: 1024px){

  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
</style></head>
      <body>
        <div id="root"> <div class="min-h-screen flex flex-col bg-background text-foreground font-sans"><header class="fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-elegant bg-background/90 backdrop-blur-lg shadow-soft border-b border-border" style="transform: translateY(-100px) translateZ(0px);"><div class="container mx-auto px-4 sm:px-6 lg:px-8"><div class="flex items-center justify-between h-20"> <a class="flex items-center space-x-2.5 group" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-7 w-7 md:h-8 md:w-8 text-primary transition-transform duration-300 ease-elegant group-hover:scale-105"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg><span class="text-2xl md:text-3xl font-bold font-professional-mono tracking-tight text-foreground group-hover:text-primary/90 transition-colors duration-200">TRIADA</span></a><nav class="hidden md:flex space-x-2"><a class="flex items-center px-3.5 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200 ease-elegant
            font-professional-sans
            text-muted-foreground hover:text-foreground hover:bg-secondary/10" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/about"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-2 transition-colors duration-200 text-muted-foreground group-hover:text-foreground"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg>About</a><a class="flex items-center px-3.5 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200 ease-elegant
            font-professional-sans
            text-muted-foreground hover:text-foreground hover:bg-secondary/10" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/achievements"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-2 transition-colors duration-200 text-muted-foreground group-hover:text-foreground"><line x1="18" x2="18" y1="20" y2="10"></line><line x1="12" x2="12" y1="20" y2="4"></line><line x1="6" x2="6" y1="20" y2="14"></line></svg>Achievements</a><a class="flex items-center px-3.5 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200 ease-elegant
            font-professional-sans
            text-muted-foreground hover:text-foreground hover:bg-secondary/10" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/team"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-2 transition-colors duration-200 text-muted-foreground group-hover:text-foreground"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>Team</a><a class="flex items-center px-3.5 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200 ease-elegant
            font-professional-sans
            text-muted-foreground hover:text-foreground hover:bg-secondary/10" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/events"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-2 transition-colors duration-200 text-muted-foreground group-hover:text-foreground"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line><path d="M8 14h.01"></path><path d="M12 14h.01"></path><path d="M16 14h.01"></path><path d="M8 18h.01"></path><path d="M12 18h.01"></path><path d="M16 18h.01"></path></svg>Events</a><a class="flex items-center px-3.5 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200 ease-elegant
            font-professional-sans
            text-muted-foreground hover:text-foreground hover:bg-secondary/10" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/platform"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-2 transition-colors duration-200 text-muted-foreground group-hover:text-foreground"><polyline points="4 17 10 11 4 5"></polyline><line x1="12" x2="20" y1="19" y2="19"></line></svg>Platform</a><a aria-current="page" class="flex items-center px-3.5 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200 ease-elegant
            font-professional-sans
            text-primary bg-primary/5" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/blog"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-2 transition-colors duration-200 text-primary"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" x2="8" y1="13" y2="13"></line><line x1="16" x2="8" y1="17" y2="17"></line><line x1="10" x2="8" y1="9" y2="9"></line></svg>Blog</a><a class="flex items-center px-3.5 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200 ease-elegant
            font-professional-sans
            text-muted-foreground hover:text-foreground hover:bg-secondary/10" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/contact"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-2 transition-colors duration-200 text-muted-foreground group-hover:text-foreground"><path d="m22 2-7 20-4-9-9-4Z"></path><path d="M22 2 11 13"></path></svg>Contact</a></nav><div class="hidden md:flex items-center space-x-2"><a href="https://github.com/" target="_blank" rel="noopener noreferrer" aria-label="GitHub" class="inline-flex items-center justify-center text-sm font-medium ring-offset-background transition-all duration-200 ease-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-60 font-professional-sans tracking-normal active:scale-[0.98] h-10 w-10 text-muted-foreground hover:text-primary hover:bg-primary/10 rounded-lg"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></a><a href="https://discord.com/" target="_blank" rel="noopener noreferrer" aria-label="Discord" class="inline-flex items-center justify-center text-sm font-medium ring-offset-background transition-all duration-200 ease-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-60 font-professional-sans tracking-normal active:scale-[0.98] h-10 w-10 text-muted-foreground hover:text-primary hover:bg-primary/10 rounded-lg"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg></a></div><div class="md:hidden"><button class="inline-flex items-center justify-center text-sm font-medium ring-offset-background transition-all duration-200 ease-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-60 font-professional-sans tracking-normal hover:bg-accent active:scale-[0.98] h-10 w-10 text-foreground hover:text-primary rounded-lg" aria-label="Toggle menu"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></div></header><main class="flex-grow container mx-auto px-4 py-8 pt-24 md:pt-32"><div style="opacity: 0.999512; transform: translateY(0.0097656px) translateZ(0px);"><div class="space-y-16 md:space-y-20"><h1 class="text-5xl md:text-6xl font-black text-center font-professional-sans text-foreground mb-12"><span class="inline-block" style="opacity: 0;"><span class="inline-block mr-[0.25em]"><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">T</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">R</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">I</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">A</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">D</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">A</span></span><span class="inline-block mr-[0.25em]"><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">I</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">n</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">t</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">e</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">l</span></span><span class="inline-block mr-[0.25em]"><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">&amp;</span></span><span class="inline-block mr-[0.25em]"><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">R</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">e</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">s</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">e</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">a</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">r</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">c</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">h</span></span><span class="inline-block mr-[0.25em]"><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">D</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">i</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">v</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">i</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">s</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">i</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">o</span><span style="display: inline-block; opacity: 0; transform: translateY(20px) translateZ(0px);">n</span></span></span></h1><div class="grid md:grid-cols-2 gap-8"><div class="h-full" style="opacity: 0; transform: translateY(25px) translateZ(0px);"><div class="rounded-xl border bg-card text-card-foreground shadow-soft professional-card h-full professional-card hover:border-primary/30 flex flex-col group"><div class="flex flex-col space-y-2 p-6 pb-3"> <h3 class="md:text-2xl font-semibold tracking-tight text-foreground text-xl font-professional-sans group-hover:text-primary transition-colors duration-200"><a href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/blog/cybermatrix-exploit-analysis">Advanced Exploit Chains: A Case Study from "CyberMatrix"</a></h3><div class="flex flex-wrap gap-x-4 gap-y-1.5 text-xs text-muted-foreground font-professional-mono mt-2"><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line><path d="M8 14h.01"></path><path d="M12 14h.01"></path><path d="M16 14h.01"></path><path d="M8 18h.01"></path><path d="M12 18h.01"></path><path d="M16 18h.01"></path></svg>May 15, 2025</span><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="10" r="3"></circle><path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662"></path></svg>Dr. E. Reed (Nyx)</span><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><path d="M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z"></path><path d="M7 7h.01"></path></svg>CTF Analysis</span></div></div><div class="p-6 font-professional-sans flex-grow pt-2 pb-4"> <p class="text-base text-muted-foreground mb-4 leading-relaxed">A detailed deconstruction of the CyberMatrix CTF, focusing on multi-stage binary exploitation and sophisticated reverse engineering approaches utilized by TRIADA operatives.</p></div><div class="p-6 pt-0"> <a class="inline-flex items-center justify-center rounded-lg font-medium ring-offset-background transition-all duration-200 ease-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-60 tracking-normal underline-offset-4 hover:underline active:scale-[0.98] text-primary p-0 h-auto font-professional-sans group-hover:underline text-base" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/blog/cybermatrix-exploit-analysis">Read Full Analysis <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></a></div></div></div><div class="h-full" style="opacity: 0; transform: translateY(25px) translateZ(0px);"><div class="rounded-xl border bg-card text-card-foreground shadow-soft professional-card h-full professional-card hover:border-primary/30 flex flex-col group"><div class="flex flex-col space-y-2 p-6 pb-3"> <h3 class="md:text-2xl font-semibold tracking-tight text-foreground text-xl font-professional-sans group-hover:text-primary transition-colors duration-200"><a href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/blog/nextgen-evasion-techniques">The Evolution of Stealth: Next-Generation Evasion Methodologies</a></h3><div class="flex flex-wrap gap-x-4 gap-y-1.5 text-xs text-muted-foreground font-professional-mono mt-2"><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line><path d="M8 14h.01"></path><path d="M12 14h.01"></path><path d="M16 14h.01"></path><path d="M8 18h.01"></path><path d="M12 18h.01"></path><path d="M16 18h.01"></path></svg>April 28, 2025</span><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="10" r="3"></circle><path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662"></path></svg>S. Volkov (Zero)</span><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><path d="M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z"></path><path d="M7 7h.01"></path></svg>Offensive Security Research</span></div></div><div class="p-6 font-professional-sans flex-grow pt-2 pb-4"> <p class="text-base text-muted-foreground mb-4 leading-relaxed">An exploration of cutting-edge techniques for bypassing contemporary detection systems and maintaining persistent, low-observable access in dynamic environments.</p></div><div class="p-6 pt-0"> <a class="inline-flex items-center justify-center rounded-lg font-medium ring-offset-background transition-all duration-200 ease-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-60 tracking-normal underline-offset-4 hover:underline active:scale-[0.98] text-primary p-0 h-auto font-professional-sans group-hover:underline text-base" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/blog/nextgen-evasion-techniques">Read Full Analysis <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></a></div></div></div><div class="h-full" style="opacity: 0; transform: translateY(25px) translateZ(0px);"><div class="rounded-xl border bg-card text-card-foreground shadow-soft professional-card h-full professional-card hover:border-primary/30 flex flex-col group"><div class="flex flex-col space-y-2 p-6 pb-3"> <h3 class="md:text-2xl font-semibold tracking-tight text-foreground text-xl font-professional-sans group-hover:text-primary transition-colors duration-200"><a href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/blog/smart-contract-audit-hacker-guide">Auditing Smart Contracts: A Practical Compendium for Hackers</a></h3><div class="flex flex-wrap gap-x-4 gap-y-1.5 text-xs text-muted-foreground font-professional-mono mt-2"><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line><path d="M8 14h.01"></path><path d="M12 14h.01"></path><path d="M16 14h.01"></path><path d="M8 18h.01"></path><path d="M12 18h.01"></path><path d="M16 18h.01"></path></svg>March 10, 2025</span><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="10" r="3"></circle><path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662"></path></svg>M. Chen (Cipher)</span><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><path d="M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z"></path><path d="M7 7h.01"></path></svg>Web3 &amp; Blockchain Security</span></div></div><div class="p-6 font-professional-sans flex-grow pt-2 pb-4"> <p class="text-base text-muted-foreground mb-4 leading-relaxed">A comprehensive overview of prevalent smart contract vulnerabilities, combined with pragmatic exploitation strategies relevant to both CTF challenges and real-world security assessments.</p></div><div class="p-6 pt-0"> <a class="inline-flex items-center justify-center rounded-lg font-medium ring-offset-background transition-all duration-200 ease-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-60 tracking-normal underline-offset-4 hover:underline active:scale-[0.98] text-primary p-0 h-auto font-professional-sans group-hover:underline text-base" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/blog/smart-contract-audit-hacker-guide">Read Full Analysis <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></a></div></div></div><div class="h-full" style="opacity: 0; transform: translateY(25px) translateZ(0px);"><div class="rounded-xl border bg-card text-card-foreground shadow-soft professional-card h-full professional-card hover:border-primary/30 flex flex-col group"><div class="flex flex-col space-y-2 p-6 pb-3"> <h3 class="md:text-2xl font-semibold tracking-tight text-foreground text-xl font-professional-sans group-hover:text-primary transition-colors duration-200"><a href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/blog/osint-strategic-intelligence">OSINT Strategies: Transforming Public Information into Strategic Intelligence</a></h3><div class="flex flex-wrap gap-x-4 gap-y-1.5 text-xs text-muted-foreground font-professional-mono mt-2"><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line><path d="M8 14h.01"></path><path d="M12 14h.01"></path><path d="M16 14h.01"></path><path d="M8 18h.01"></path><path d="M12 18h.01"></path><path d="M16 18h.01"></path></svg>February 20, 2025</span><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="10" r="3"></circle><path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662"></path></svg>A. Khan (Glitch)</span><span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 mr-1.5 text-primary/70"><path d="M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z"></path><path d="M7 7h.01"></path></svg>Intelligence &amp; Reconnaissance</span></div></div><div class="p-6 font-professional-sans flex-grow pt-2 pb-4"> <p class="text-base text-muted-foreground mb-4 leading-relaxed">Advanced frameworks and open-source tools for effective Open Source Intelligence (OSINT), crucial for comprehensive target profiling in pre-engagement phases.</p></div><div class="p-6 pt-0"> <a class="inline-flex items-center justify-center rounded-lg font-medium ring-offset-background transition-all duration-200 ease-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-60 tracking-normal underline-offset-4 hover:underline active:scale-[0.98] text-primary p-0 h-auto font-professional-sans group-hover:underline text-base" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/blog/osint-strategic-intelligence">Read Full Analysis <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></a></div></div></div></div><div class="text-center pt-4"><button class="inline-flex items-center justify-center font-medium ring-offset-background transition-all duration-200 ease-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-60 font-professional-sans tracking-normal border border-input bg-transparent hover:bg-accent hover:text-accent-foreground shadow-minimal active:scale-[0.98] h-12 rounded-lg px-8 text-base btn-clean-outline">View Archived Publications</button></div><div class="rounded-xl border bg-card text-card-foreground shadow-soft professional-card professional-card hover:border-secondary/30 text-center"><div class="flex flex-col space-y-2 p-6 pb-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-14 h-14 text-secondary mx-auto mb-3"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" x2="8" y1="13" y2="13"></line><line x1="16" x2="8" y1="17" y2="17"></line><line x1="10" x2="8" y1="9" y2="9"></line></svg><h3 class="md:text-2xl font-semibold tracking-tight font-professional-sans text-secondary text-3xl">Commitment to Knowledge</h3></div><div class="p-6 pt-0 font-professional-sans text-lg text-muted-foreground leading-relaxed"><p class="max-w-2xl mx-auto">TRIADA is dedicated to the dissemination of knowledge within the cybersecurity domain. Our publications offer insights into CTF strategies, vulnerability research, and emerging cyber threats, contributing to a more secure digital ecosystem.</p></div></div></div></div></main><footer class="bg-background border-t border-border py-12 text-muted-foreground"><div class="container mx-auto px-4 sm:px-6 lg:px-8"><div class="grid grid-cols-1 md:grid-cols-3 gap-8 items-center"><div class="flex flex-col items-center md:items-start text-center md:text-left"><a class="flex items-center space-x-2 mb-2 group" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-7 w-7 text-primary transition-colors group-hover:text-primary/80"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg><span class="text-xl font-bold font-professional-mono text-foreground group-hover:text-primary/90 transition-colors">TRIADA</span></a><p class="text-sm">© 2025 TRIADA. All rights reserved.</p><p class="text-xs mt-1">Elite Cybersecurity Collective.</p></div><nav class="flex flex-wrap justify-center md:justify-center gap-x-5 gap-y-2 text-sm font-professional-sans"><a class="hover:text-primary transition-colors" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/about">About</a><a class="hover:text-primary transition-colors" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/achievements">Achievements</a><a class="hover:text-primary transition-colors" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/blog">Blog</a><a class="hover:text-primary transition-colors" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/platform">Platform</a><a class="hover:text-primary transition-colors" href="https://a91b1438-8693-40a3-973b-a66b3e72d384.dev27.app-preview.com/contact">Contact</a></nav><div class="flex justify-center md:justify-end space-x-5"><a href="https://github.com/" target="_blank" rel="noopener noreferrer" aria-label="GitHub" class="text-muted-foreground hover:text-primary transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></a><a href="https://discord.com/" target="_blank" rel="noopener noreferrer" aria-label="Discord" class="text-muted-foreground hover:text-primary transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg></a></div></div><p class="text-xs text-center mt-10 text-muted-foreground/60 font-professional-sans">Disclaimer: TRIADA is a conceptual entity for demonstration purposes. All activities portrayed are simulated.</p></div></footer></div><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events: none;"><ol tabindex="-1" class="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]"></ol></div></div>
        <script type="module" src="./TRIADA __ Elite CTF blog_files/main.jsx"></script>
      
    
  </body></html>